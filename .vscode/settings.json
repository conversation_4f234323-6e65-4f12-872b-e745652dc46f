{"files.autoSave": "off", "files.autoSaveDelay": 500, "dart.flutterHotReloadOnSave": "manual", "dart.hotReloadOnSave": "always", "editor.formatOnSave": true, "[dart]": {"editor.formatOnSave": true, "editor.formatOnType": true, "editor.rulers": [80], "editor.selectionHighlight": false, "editor.suggest.snippetsPreventQuickSuggestions": false, "editor.suggestSelection": "first", "editor.tabCompletion": "onlySnippets", "editor.wordBasedSuggestions": "off"}}