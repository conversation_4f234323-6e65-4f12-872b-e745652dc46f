import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/home_controller.dart';

class HomeView extends GetView<HomeController> {
  const HomeView({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Home'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView.builder(
        itemCount: 20,
        itemBuilder: (context, index) {
          return ListTile(
            leading: CircleAvatar(child: Text('${index + 1}')),
            title: Text('Item ${index + 1}'),
            subtitle: Text('Description ${index + 1}'),
            trailing: const Icon(Icons.arrow_forward_ios),
            onTap:
                () => Get.snackbar(
                  'Tapped',
                  'You tapped item ${index + 1}',
                  snackPosition: SnackPosition.BOTTOM,
                ),
          );
        },
      ),
    );
  }
}
